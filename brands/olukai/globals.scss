@import '../shared/globals.scss';

/* Color Guidelines
  ==========================================================================
  primary: Highest level user attention.x
  secondary: Second highest level user attention.
  tertiary: Third highest level user attention.
  light: Most prominent light background color. Must be able to overlay on top of dark.
  dark: Most prominent dark background color. Must be able to overlay on top of light.
  pop: Usage examples are badges.
  highlight: Think about this as using a highlighter pen.
  body: Most common text color.
  header: Most common text color for headers.
*/

:root {

  --color-primary: #021327; // #0C4066;
  --color-secondary: #ff4438; // #0066D2;
  --color-tertiary: #F6f6f6;
  --color-light: #FFFFFF;
  --color-dark: #666666;
  --color-pop: #DFEFF0;
  --color-highlight: #F1A407;
  --color-body: #021327; // #381300;

  /*

  --swiper-theme-color: #000;

  --header-height:76px;
  --preheader-height: 34px;
  --header-height-half:38px;
  --unscrolled-header-height: 100px;
  --scrolled-header-height: 76px;

  @media screen and (max-width: 1024px) {
    --header-height:100px;
    --preheader-height: 32px;
    --header-height-half: 34px;
    --unscrolled-header-height: 100px;
    --scrolled-header-height: 68px;
  }

  --font-body-weight: normal;
  --font-body-style: normal;
  --font-heading-weight: normal;
  --font-heading-style: normal;
  --font-subheading-weight: normal;
  --font-subheading-style: normal;

  
  --header-height:48px;
  
  */
}




.top-main {
  top:var(--header-offset)
}
@media screen and (min-width: 1025px) {
  .lg\:top-main {
    top:var(--header-offset);
  }
}

.rte a {
  @apply underline text-secondary;
}

 

   /* Type Styles
========================================================================== 
 Guidelines
==========================================================================
  **Type Styles**

  primary: Primary Headline or Title type.
  secondary: Commonly used as Subtitle type, compliments Primary.
  tertiary: Third highest level user attention, smaller but stylistically similar to primary.

  **Type Layouts / Spacing**

  page: Most common text color.
  section: Most common text style for headers.
  article: Most common text style for headers.

*/

.type {

  &-hero {
    font-size: 2.0625rem;
    line-height: 108%;
    @apply font-olukai-bold;
    letter-spacing: -0.99px;
    font-weight:normal;
    &.type--sm {
      font-size: 1.75rem;
      letter-spacing: -0.84px;
    }
    &.type--lg {
      letter-spacing: -1.8px;
      font-size: 3.75rem;
      line-height: 90%;
    }
  }
  &-headline {
    font-size: 2.0625rem;
    line-height: 108%;
    letter-spacing: -0.99px;
    @apply font-heading;
    font-weight:500;
    &.type--sm {
      font-size: 1.75rem;
      letter-spacing: -0.84px;
    }
    &.type--lg {
      letter-spacing: -1.8px;
      font-size: 3.75rem;
      line-height: 90%;
    }
  }
  &-subline {
    font-size: 16px;
    line-height: 1.15;
    &.type--sm {
      font-size: 14px;
    }
    &.type--lg {
      font-size: 18px;
    }
  }
  &-micro {
    font-weight:400;
    font-size: .8125rem;
    line-height: 108%;
    letter-spacing: -0.13px;
    font-family: var(--font-regular);
    &.type--sm {
      font-size: .75rem;
      letter-spacing: -0.12px;
    }
    &.type--lg {
      font-size: .875rem;
      letter-spacing: -0.14px;
    }
  }
  &-item {
    font-size: 14px;
    line-height: 1.15;
    @apply font-heading;
    font-weight:normal;
    &.type--sm {
      font-size: 12px;
    }
    &.type--lg {
      font-size: 18px;
      line-height: 1.5;
    }
  }
  &-section {
    font-size: 1.25rem;
    line-height: 132%;
    letter-spacing: -0.6px;
    @apply font-heading;
    font-weight:500;
    &.type--sm {
      font-size: 1.125rem;
      letter-spacing: -0.54px;
    }
    &.type--lg {
      font-size: 1.375rem;
      letter-spacing: -0.66px;
    }
  }
  &-eyebrow {
    @apply font-heading;
    font-size: .8125rem;
    line-height: 108%;
    letter-spacing: -0.13px;
    font-weight: 500;
    &.type--sm {
      font-size: .8125rem;
		letter-spacing: -0.39PX;
    line-height: 120%;
    }

    &.type--lg {
      font-size: .875rem;
      letter-spacing: -0.14px;
    }
  }
  &-body {
    
    font-weight:normal;
      font-size: 18px;
      line-height: 120%;
      letter-spacing: -0.54px;
      font-family: var(--font-regular);
      strong {
        font-family: var(--font-medium);
        font-weight: 500;
      }
    &.type--sm {
      font-size: .9375rem;
      letter-spacing: -0.45px;
    }
    &.type--lg {
      font-family: var(--font-medium);
    }
  }
  &-nav-link {
    @apply font-heading text-[17px] text-primary;
    font-weight:normal;
  }
}



/* Buttons and Controls
========================================================================== */

.button, .btn {

  @apply gap-2 whitespace-nowrap items-center border  inline-flex text-[16px] h-[43px] justify-center opacity-100 overflow-hidden py-0 px-[12px] relative cursor-pointer;
  font-family: var(--font-medium);
  border-radius: 9px;

  svg {
    width:16px;
    height:16px;
    stroke-width:2px;
    stroke:currentColor;
    position:relative;
  }

  &--large {
    @apply h-[56px];
  }

  &--primary { @apply  text-white ; 
    background: var(--color-secondary);
    border: 1px solid var(--color-secondary) ;
    &:hover{
      background-color: var(--color-secondary);
      border: 1px solid var(--color-secondary);
      opacity: .8;
    }
    &:focus{
      background: var(--color-secondary);
      border: 1px solid var(--color-secondary) ;
    }
  }
  &--secondary { @apply border-white bg-white ; 
    color: var(--color-body);
    &:hover{
      border-color: #cccccc;
      background-color: #cccccc;
      color: var(--color-body);
    }
    &:focus{
      @apply border-white bg-white ; 
      color: var(--color-body);
    }
  }
  &--tertiary {
    background: var(--color-primary); 
    color: var(--color-light); 
    border: 1px solid var(--color-primary);

    &:hover{
      color: var(--color-light);
    }
    &:focus{
      background: var(--color-light); 
      color: var(--color-primary); 
      border: 1px solid var(--color-light);
    }
  }
  &--light {  
    @apply  bg-transparent ;
    color: var(--color-secondary);
    border: 1px solid var(--color-secondary);
    &:before {
      @apply bg-secondary;
    }
    &:hover{
      background-color: var(--color-secondary);
      color: var(--color-light);
    }
  }
  &--dark { 
    @apply  bg-transparent ;
    color: var(--color-body); 
    border: 1px solid var(--color-body);
    &:before {
      @apply bg-black;
    }
  }
  &--pop { 
    @apply bg-pop border-none min-w-[120px] shrink-0; 
    color: var(--color-body);
    &:hover {
			@apply text-primary;
    }
  }
  &--light, &--dark, &--highlight {
    &:before {
      border-radius: 50%;
      content: '';
      height: 400px;
      left: 50%;
      pointer-events: none;
      position: absolute;
      top: 50%;
      transform: translate(-50%, -50%) scale(0);
      transform-origin: center center;
      transition: transform 0.25s ease, opacity 0.25s ease-out, color 0.1s ease;
      width: 400px;
      opacity: 0;
      will-change: transform, opacity, color;
    }
    &:hover , a:hover & {
      @apply text-white;
      z-index: 1; //required for hover background effect to work
      
      &:before {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
        transition: transform .4s ease-out,opacity .3s ease,color .1s ease;
      }
    }
		> span:not(.sr-only) {
			@apply relative z-10;
		}
  }
  &--highlight { 
    @apply bg-transparent border-white text-white hover:text-primary; 
    a:hover & {
      @apply text-primary;
    }
    &:before {
      @apply bg-white;
    } 
  }
 
  &--w-icon { 
    @apply flex justify-center items-center border-secondary bg-transparent text-secondary gap-3;
    &:before {
      @apply bg-secondary;
    }
  }
  &--simple {
    font-family: var(--font-regular);
		@apply tracking-wide text-primary text-sm gap-1 capitalize border-transparent px-0;
    svg {
      width:14px;
      height:14px;
      stroke-width:3px;
    }
  }
  &--icon { 
    @apply bg-transparent border-transparent gap-0 px-md; 
  }
  &--emphasis {
    border:none;
    text-transform:none;
    font-family: var(--font-regular);
    letter-spacing:0;
    padding-left:0;
    padding-right:0;
    color: var(--color-body);  /* #381300 */
    font-style: italic;
    svg {
      width:24px;
      height:24px;
      stroke-width:3px;
    }
    &:has(.button__text~.icon) {
      .button__text{
        margin-right:10px;
      }
    }
    &:has(.icon~.button__text) {
      .button__text{
        margin-left:10px;
      }
    }
  }
  &--micro {
    font-family: var(--font-regular);
    @apply tracking-wide text-primary text-sm gap-1 capitalize border-transparent px-0;
    svg {
      width:14px;
      height:14px;
      stroke-width:3px;
    }
    &:has(.button__text~.icon) {
      
    }
    &:has(.icon~.button__text) {
      .button__text{
        margin-right:20px;
      }
    }
  }

  &--link { 
    @apply bg-transparent text-[13px] p-0 border-0 underline tracking-normal ;
    color: var(--color-primary); 
  }
  &--light-text-link { 
    @apply bg-transparent text-[13px] p-0 border-0 underline tracking-normal ;
    color: var(--color-light); 
  }
  &--micro-link { 
    @apply bg-transparent text-[12px] text-primary p-0 border-0 underline;
    font-family: var(--font-regular);
    letter-spacing: -0.1px;
    text-transform:none;
  }

  &--primary-hover { 
    @apply border-secondary ;
    background: var(--color-secondary); 
    color: var(--color-light);
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }

  &--secondary-hover { 
    @apply border-white ;
    background: var(--color-light); 
    color: var(--color-body);
    &:hover , a:hover & {
      border-color: #cccccc;
      background-color: #cccccc;
      color: var(--color-body);
    }
    &:focus{
      @apply border-white bg-white ; 
      color: var(--color-body);
    }
  }

  &--tertiary-hover { 
    @apply bg-primary border-primary text-white;
    &:hover , a:hover & {
      @apply opacity-70;
    }
  }

  &--disabled,  &[disabled] {
    @apply bg-[#eee] border-[#eee] text-black cursor-not-allowed;
  }
  &--large {
    @media screen and (min-width: 1025px) {
      height:56px; padding:0 50px; border-width:2px;
    }
  }
  &--action {
    border-radius:1000px;
    min-width:auto!important;
    font-size:13px;
    font-family:var(--font-regular);
    text-transform:none;
    svg {
      width:14px;
      height:14px;
    }
    &:has(.button__text~.icon) {
      padding-right:6px;
    }
    &:has(.icon~.button__text) {
      padding-left:6px;
    }
  }

	&.open {
		flex-direction:row-reverse;
		.icon {
			rotate: 180deg;
		}
	}

  &:has(.icon) {
    .icon {
      margin-left:0px;
			margin-right:0px;
    }
  }
}



/* Forms
========================================================================== */

.field {
  
  &__input,
  &__textarea,
  &__select {
    @apply border-[#ded1be] text-base lg:text-sm;
  }

  &__select {
    @apply rounded-[2px] h-12 py-0 text-sm appearance-none;

    background-position: right 12px center;
    background-image: url(https://olukai.com/cdn/shop/t/405/assets/select-icon.svg?v=43184182532122065261626389908);
    background-repeat: no-repeat;
    cursor: pointer;
  }

  &__toggle {
    @apply bg-primary;

    label {
      @apply inline-flex items-center;

      input {
        &:not(:checked) {
          & ~ .toggle__label {
            @apply cursor-pointer text-[12px] px-3 py-1 border bg-transparent border-transparent rounded-full text-white;
            @media only screen and (max-width: 1280px) and (min-width: 1023px) {
              @apply text-[9px]
            }
          }
        }

        &:checked {
          & ~ .toggle__label {
            @apply text-[12px] px-3 py-1 border bg-white border-[#d3c7c1] text-[#381300] rounded-full;
            @media only screen and (max-width: 1280px) and (min-width: 1023px) {
              @apply text-[9px]
            }
          }
        }
      }
    }
  }

  &__image {
    @apply border rounded p-[15px] relative flex flex-col items-center justify-end;
    border-color: #d6d6d6;
    color: var(--color-body); // #381300;
    font-weight: 700;
    line-height: 18px;
    letter-spacing: 1.5px;
    input {
      @apply absolute inset-0 opacity-0;
    }
    img {
      width: 155px;
      max-width: 100%;
    }
    label {
      @apply flex items-center flex-col justify-end text-center uppercase text-[15px] gap-2.5;
    }
    &--horizontal {
      @apply justify-start items-start;
      label {
        @apply flex-row;
        img {
          width: 72px;
        }
      }
    }
    &:hover, &:focus {
      border-color: #042c4b;
      -webkit-box-shadow: 0 5px 6px rgba(0,0,0,.1);
      box-shadow: 0 5px 6px #0000001a;
    }
    &:has(input:checked) {
      border: 2px solid #042c4b;
      background: #f0f8f8;
      -webkit-box-shadow: 0 5px 6px rgba(0,0,0,.1);
      box-shadow: 0 5px 6px #0000001a;
    }
  }
  
  &__description {
    @apply mt-xs;
    a {
      color:var(--color-secondary);
    } 
  }
}

.field {
  input {
    &[type="checkbox"],
    &[type="radio"] {
      @apply h-[18px] w-[18px];

      & + span {
        @apply capitalize;
      }
    }  

    &[type="radio"] {
      &:checked {
        @apply border-0 shadow-[inset_0_0_0_7px] shadow-secondary;
      }
    }
  }

  &__chip {
    @apply bg-tertiary rounded-[3px] px-5 py-2.5 font-heading flex-row items-center;

    span {
      font-family: var(--font-regular);
      font-weight: 400;
      font-size: 13px;
      text-transform: capitalize;
      line-height: normal;
    }

    &:has(button) {
      @apply pr-3;
    }

    button {
      @apply pl-2.5;
    }

    &-swatch {
      @apply outline-offset-0 outline-[4px];
    }
    
    &:hover {
      &-swatch {
        @apply outline-primary;
      }
    }
    
    &:has(input:checked) {
      @apply bg-primary text-white;
    }
  }

  &__checkbox {

    span { 
      text-transform: capitalize;
    }
    
    input[type="checkbox"] {
      @apply rounded-none relative;

      &:checked {
        @apply border-0 bg-secondary shadow-none;
    
        &:after {
          content: "";
          display: block;
          width: 5px;
          height: 8px;
          border: solid white;
          border-width: 0 1.5px 1.5px 0;
          position: absolute;
          transform: rotate(45deg);
          top: 50%;
          left: 50%;
          translate: -50% -65%;
          background: transparent;
        }
      }
    }
  }
  
  &__color {
    &-swatch {
      @apply outline-offset-0 outline-[4px];
    }
    
    &:hover &-swatch {
      @apply outline-primary;
    }

    span {
      font-family: var(--font-regular);
      font-weight: 400;
      @apply text-dark text-[12px];
    }

    &:has(input:checked),
    &:hover {
      span {
        @apply text-primary;
      }
    }
  }

  &__buttons {
    @apply gap-[9px];
  }

  &__button {
    @apply w-full h-full rounded aspect-1 flex-auto;

    &-text {
      @apply font-body text-[13px] [input:checked_~_&]:border-primary [input:checked_~_&]:bg-primary [input:checked_~_&]:text-white rounded;
    }

    &:hover &-text {
      @apply border-primary border-2;
    }
  }
}

.field--floating-label {
  @apply bg-transparent relative mb-4 text-gray-700;

  label {
    @apply absolute text-[11px] left-3 top-1.5 text-gray-400 opacity-100 pointer-events-none transition-all duration-200 ease-linear;
  }

  input, textarea, select {
    @apply pt-5;
  }

  &:has(:placeholder-shown), &:has(option:checked[disabled]) {
    label {
      @apply opacity-0;
    }
    input, select, textarea {
      @apply pt-1 pb-1;
    }
  }
}


/* Accordion
========================================================================== */

.list {
  .accordion {
    &:last-child {
      @apply border-b border-gray-300;
    }
  }
}

.accordion {
  &:last-child {
    @apply border-b border-gray-300;
  }
  .accordion-title {
    @apply bg-transparent border border-gray-300 border-l-0 border-b-0 border-r-0 text-left cursor-pointer h-[60px] leading-[60px];
    font-family: var(--font-medium);
    span:first-child {}
    .accordion-control {
      @apply bg-transparent text-[#021327] border-0;
      .icon {}
    }
  }
  .accordion-panel {
    @apply text-sm py-4 px-0 border-0;
    > div {}
    ul {}
    p:last-child {}
  }
  &-control {
    &:first-of-type {
      display: none;
    }
    &:last-of-type {
      display: block;
    }

    // Plus icon animation using pseudo-elements overlay
    position: relative;

    // Hide the original plus icon and create our own animated version
    .icon {
      opacity: 0;
    }

    // Create animated plus/minus with pseudo-elements
    &::before,
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      background: currentColor;
      border-radius: 0.6px;
      transition: all 0.3s ease;
      transform-origin: center;
      will-change: transform;
    }

    // Horizontal line (always visible)
    &::before {
      width: 12px;
      height: 1.2px;
      transform: translate(-50%, -50%);
    }

    // Vertical line (disappears when accordion opens)
    &::after {
      width: 1.2px;
      height: 12px;
      transform: translate(-50%, -50%);
    }

    // When accordion is open, hide the vertical line (creating minus effect)
    [open] > summary & {
      &:first-of-type {
        display: none;
      }
      &:last-of-type {
        display: block;
      }

      &::before {
        opacity: 0;
        transform: translate(-50%, -50%) rotate(180deg);
      }

      &::after {
        transform: translate(-50%, -50%) rotate(90deg);
      }
    }

    // When accordion is closing, force the closed state with transitions
    .accordion-closing[open] > summary & {
      &:first-of-type {
        display: block;
      }
      &:last-of-type {
        display: none;
      }

      // Force the pseudo-elements back to closed state, but let them transition
      &::before {
        opacity: 1 !important;
        transform: translate(-50%, -50%) rotate(0deg) !important;
      }

      &::after {
        transform: translate(-50%, -50%) rotate(0deg) !important;
      }
    }
  }
}

/* Pagination
========================================================================== */

.pagination--page {
  @apply bg-transparent;
  li {
    @apply flex justify-start items-start;
    a:not(.pp-control),
    button:not(.pp-control), > span {
      @apply block px-2 py-0.5 pb-1 mx-[5px];
    }
    [aria-current="page"], .active {
      @apply relative block;
      &:after {
        content: "";
        @apply block border-b-[3px] border-body;
      }
    }
  }
}

.swiper-pagination,
.pagination {
  .pagination-bullet, .swiper-pagination-bullet {
    @apply bg-[#d7d2cb] w-[5px] h-[5px] rounded-full mx-2;
    &.active, &.swiper-pagination-bullet-active {
      @apply scale-[1.9];
    }
  }

  @apply flex items-center;

  .prev, .next {

    .icon {
      height:18px;
      stroke-width:2px;
      color:#797979;
    }

  }
  .deco {
    color:#797979;
  }
  .page {
    @apply flex items-center justify-center font-bold w-8 h-8 rounded-full;
    color:#797979;
    &.current { 
      @apply  bg-secondary text-white; 
    }
  }

}

.btn-control {
  &.swiper-button-prev,
  &.swiper-button-next {
    @apply bg-white rounded-full h-10 w-10 shadow-md text-inherit flex p-0;
  }
}

/* Mini Form
========================================================================== */

details.no-close[open] {
  summary { pointer-events: none; }
}

.mini-form {
  summary {
    & > span {
      width: 100%;
      transition: width 400ms ease-out, opacity 400ms ease-out;
      z-index: 10;
    }
   
  }
  & > div {
    display:grid;
    transition:max-height 400ms ease-out;
    max-height:0px;
  }
  &[open] {
    & > div {
      max-height:200px;
    }
  }
  form {
    input {
      font-size:13px;
      padding:6px 12px;
      @apply h-14 rounded-r-none rounded-l-[3px] lg:h-[39px];
    }
    button {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      @apply lg:h-auto relative z-20;
    }
    input:not([valid="true"]) + button {
      cursor:not-allowed;
    }
  }
  &[open] {
    summary {
      span,
      span svg {
        opacity:0;
        width:0;
        @apply bg-dark text-dark;
      }
    }
    form {
      margin-top:-56px;
      @media only screen and (min-width: 1024px) {
        margin-top:-39px;
      }
    }
  }
  &__info {
    p {
      text-align: left;
      margin:0.75rem 0 ;
      font-size:11px;
      color:#444;
      font-weight: 400;
      letter-spacing: -0.11px;
      line-height: 120%;
      font-family: var(--font-regular);
    }
  }
  .button {
    @apply max-lg:h-14 gap-3;
  }
  &__success {
    visibility:hidden;
    width:0;
    height:0;
    display:none;
    opacity:0;
    transition:opacity 300ms ease;
  }
  &--submitted {
    summary {
      pointer-events:none;
      span {
        display:none;
      }
    }
    .mini-form__success {
      visibility:visible;
      display:block;
      width:100%;
      height:auto;
      opacity:1;
    }
  }
}



.dropdown-menu {
  font-size:12px;
  &__trigger {
    border: 1px solid #F0EAE4;
    border-radius: 3px;
    font-size:12px;
    color:var(--color-body); // #381300;
    padding:4px 0;
    @apply rounded-sm;
    background: var(--color-light);
  }
  &__label {
    color:#797979;
    padding:4px 4px 4px 16px;
  }
  &__value {
    padding:4px 12px 4px 0px;
    @apply grow;
  }
  &__trigger-icon {
    width:36px;
    height:24px;
    border-left:1px solid #DED1BE;
    svg {
      width:14px;
      height:12px;
      fill:#797979;
      margin-left:-1px;
    }
  }
  &__menu {
    margin-top:1px;
    @apply rounded-sm;
    background: var(--color-light);
  }
  &__menu-item {
    &:hover {
      background-color:#eee;
    }
    button {
      @apply px-sm py-xs text-left;
      background-color:transparent;
    }
  }
}



.task-list {
  @apply grid grid-cols-2 gap-sm w-3/4;
  color:#797979;
  font-size:12px;
  .task {
    @apply flex items-center;
    .check {
      border-radius:100%;
      border:1px solid #e1d5c5;
      color:#e1d5c5;
      background: var(--color-light);
      width:18px;
      height:18px;
      display:flex;
      align-items:center;
      justify-content:center;
      @apply flex items-center justify-center mr-2;
      svg {
        display:none;
      }
    }
    &.complete {
      .check {
        border:1px solid #736b67;
        background:#736b67;
        color: var(--color-light);
        svg {
          display:block;
          width:10px;
          height:10px;
          stroke-width:2px;
        }
      }
    }
  }
}



.upsell-item {
  @apply text-sm p-[15px] border border-[#CCCCCC] rounded-md flex-col h-full;
  &-skeleton {
    @apply h-[200px] w-full flex flex-grow;
  }
  &__content {
    @apply flex pb-1.5;
  }
  &__media {
    img {
      width: 4.5rem;
      height: 4.5rem;
    }
  }
  button[disabled] {
    background-color:rgba(100,100,100,0.35);
    color: var(--color-light);
  }
  &__header {
    @apply grid grid-cols-[1fr_auto] w-full; 
    * {
      font-weight:400;
    }

  }
  &__titles {
    @apply col-start-1 col-end-3 row-start-1 row-end-2;
  }
  &__prices {
    @apply col-start-2 col-end-3 row-start-1 row-end-2;
  }
  &__title {
    font-family: var(--font-medium);
    font-size:12px;
    margin:0;
  }
  .review-snippet {
    @apply pt-1;
  }
  &__subtitle,
  &__type {
    font-size:12px;
    margin:0px 0;
  }
  &__price {
    font-family: var(--font-medium);
    font-size:12px;
    line-height:1;
    margin:0;
  }
  &__body {
    @apply w-full flex flex-col pl-[12px] flex-1;
  }
  
  &__actions {
    @apply flex justify-between w-full gap-[9px];

    label {
      width: 50%;
    }

    select {
      @apply border bg-transparent px-3 rounded-md text-[12px] leading-none w-full h-[30px] appearance-none;
      line-height:1;
      background-position: right 12px center;
      background-image: url("data:image/svg+xml,%3Csvg width='7' height='4' viewBox='0 0 7 4' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.5 0.5L3.5 3.5L6.5 0.5' stroke='black' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
      background-color: #FFFFFF;
      background-repeat: no-repeat;
      cursor: pointer;
      font-family: var(--font-regular);
    }

    button {
      @apply w-1/2 h-[30px] text-[12px];
      .icon {
        @apply hidden;
      }
    }
  }

  &__swatch {
    @apply w-[43px] h-[43px] rounded-md; 

    &es {
      @apply flex overflow-x-auto;

      &--visible {
        @apply flex gap-[4px] lg:gap-[5px];
      }

      &-container {
        @apply pb-sm relative;
      }
    }

    &-more {
      @apply flex pl-1.5 text-[12px] font-heading text-left;
      line-height: 1;
    }

    &.active {  
      @apply border border-black;
    }

  } 

}
.swiper-slide:has(.upsell-item[class*="sibling-item-"]) {
  display: none;
}

.tabs {
  button,a {
    @apply font-bold border-current border-b-2;
    &:not(.active) {
      color:#b4aaa5;
    }
  }
}

/* Micro Upsell
========================================================================== */
.micro-upsell {
  &__item {
    @apply flex-shrink-0 flex flex-col justify-between;

    .product-item {
      @apply w-full;
    }

    .product-item__actions button,
    .product-item__quick-add,
    .product-item:hover .product-item__quick-add {
      display: none;
    }

    .product-item__actions {
      opacity: 0.25;
      pointer-events: none;
    }
    &:has(input:checked) .product-item__actions {
      opacity: 1;
      pointer-events: auto;
    }

    .product-item__title-price-wrap:hover {
      @apply underline;
    }

    .field {
      @apply mb-0;
    }

    select {
      @apply border p-xs bg-transparent border-black rounded-md text-sm leading-none;
    }
  }

  &__separator {
    @apply flex-shrink-0;
  }
}

/* Tables
========================================================================== */
table {
  border-top:1px solid #EEE;
  border-bottom:1px solid #EEE;
  td {
    border-bottom:1px solid #EEE;
    border-right:1px solid #EEE;
    text-align:center;
    padding:2rem;
  }
  &.legend {
    position:sticky;
    left:0;
    td {
      background: var(--color-light);
      border-right:1px solid #EEE;
      text-align:right;
      
    }
  }
  tr:nth-of-type(odd) {
    background:#f7f7f7;
  }
}
@media screen and (max-width: 1024px) {

.desk-clear-all{
  display: none;
}
}
@media screen and (min-width: 1024px) {
.desk-clear-all .clear-all-text  {
  color: #381300;
  size: 12px;
  font-family: var(--font-medium);
  border-bottom: 1px solid #381300;
}
.desk-clear-all {
  padding-bottom: 11px;
}
}
header.header-bar #SearchModalInput label svg.icon-search {
  stroke: var(--color-primary);
}


/*  Gift Card Style =====================================  */
.giftcard {
  display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    text-align: center;

  .giftcard__container{
    margin: 50px auto 50px;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    max-width: 600px;
    padding: 0 20px;
    width: 100%;
    .giftcard__block--code{
      margin-bottom: 45px !important;
      .icon--logo {
        img{
          margin: 0 auto;
          height: 24px;
        }
      }
      .giftcard__title--page{
        margin: 24px auto 39px;
        font-size: 24px !important;
        font-family: GTA-Regular, Arial, Helvetica, sans-serif;
        letter-spacing: -.03em;
        font-weight: 500;
        line-height: 1.375;
        width: 100%;
        max-width: 260px;
      }
      .giftcard__image-container{
        position: relative;
        .giftcard__title--value{
          font-weight: 700;
          line-height: 19px;
          margin: 0;
          background-color: #d49a21;
          border-top-left-radius: 24px;
          border-bottom-right-radius: 24px;
          color:  var(--color-light);
          font-family: GTA-Bold, Arial, Helvetica, sans-serif;
          font-size: 24px;
          height: 72px;
          left: 0;
          letter-spacing: 0;
          padding: 17px 17px 0;
          position: absolute;
          top: 0;
          width: 144px;
          &:before{
            content: "Current Balance";
            display: block;
            font-size: 12px;
            margin-bottom: 6px;
            line-height: 16.5px;
          }
        } 
        span.giftcard__code{
          background: none;
          margin-top: 39px;
          display: block;
          transform: none;
          p.giftcard__text{
            margin-bottom: 0;
            color: #371300;
            font-family: GTA-Regular, Arial, Helvetica, sans-serif;
            font-size: 15px;
            letter-spacing: 0;
            line-height: 24px;
          }
          span#GiftCardDigits {
            font-family: GTA-Bold, Arial, Helvetica, sans-serif;
            font-size: 24px;
            line-height: 1;
            padding-bottom: 6px;
            color: #371300;
            letter-spacing: 0;
          }
        }     
      }
    }
    .giftcard__block.giftcard__block--left {
      width: 100%;
      p.giftcard__text.giftcard__text--balance {
        display: none;
      }
      .giftcard__buttons {
        display: none !important;
        justify-content: center;
        gap: 5px;
        align-items: baseline;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -webkit-box-pack: start;

        a.giftcard__button {
          max-width: 281px;
          height: 58px;
          width: 100%;
          line-height: 58px;
          background-color: var(--color-body);
          color:  var(--color-light);
          padding: 0;
          font-size: 18px;
          letter-spacing: 3.96px;
          font-family: interstate, sans-serif, Garamond, Baskerville, Caslon, serif;
          font-weight: 700;
          text-transform: uppercase;
          -webkit-transition: color .45s cubic-bezier(.785,.135,.15,.86), border .45s cubic-bezier(.785,.135,.15,.86);
          transition: color .45s cubic-bezier(.785,.135,.15,.86), border .45s cubic-bezier(.785,.135,.15,.86);
          z-index: 1;
          -webkit-tap-highlight-color: initial;
        }        
        a#PrintGiftCard {
          font-size: 12px;
          line-height: 21px;
          letter-spacing: .24px;
          padding-left: 15px;
          text-decoration: none;
          text-transform: capitalize;
          font-family: interstate,sans-serif,Century Gothic,sans-serif;
        }
      }
      .giftcard__buttonss {
        justify-content: center;
        gap: 24px;
        align-items: baseline;
        -webkit-box-align: center;
        -ms-flex-align: center;
        align-items: center;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        -webkit-box-orient: horizontal;
        -webkit-box-direction: normal;
        -ms-flex-flow: row wrap;
        flex-flow: row wrap;
        -webkit-box-pack: start;

        a.giftcard__button {
          max-width: 220px;
          height: 55px;
          width: 100%;
          line-height: 58px;
          border-radius: 3px;
          background-color: var(--color-secondary);
          color:  var(--color-light);
          padding: 0;
          font-size: 14px;
          letter-spacing: .56px;
          font-family: GTA-Bold,Arial,Helvetica,sans-serif;
          font-weight: 700;
          text-transform: uppercase;
          -webkit-transition: color .45s cubic-bezier(.785,.135,.15,.86), border .45s cubic-bezier(.785,.135,.15,.86);
          transition: color .45s cubic-bezier(.785,.135,.15,.86), border .45s cubic-bezier(.785,.135,.15,.86);
          z-index: 1;
          -webkit-tap-highlight-color: initial;
          &:hover{
            background-color:  var(--color-light);
            color: var(--color-secondary);
            border: 1px solid var(--color-secondary);
          }
        }  
      }
    }
  }
}

